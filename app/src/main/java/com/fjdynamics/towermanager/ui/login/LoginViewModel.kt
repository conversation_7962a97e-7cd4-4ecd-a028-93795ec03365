package com.fjdynamics.towermanager.ui.login

import androidx.lifecycle.viewModelScope
import com.fjdynamics.towermanager.data.models.UserInfo
import com.fjdynamics.towermanager.data.repository.ProjectRepository
import com.fjdynamics.towermanager.data.repository.SettingsRepository
import com.fjdynamics.towermanager.data.repository.UserRepository
import com.fjdynamics.towermanager.ui.BaseViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

data class LoginUiState(
    val inputUsername: String,
    val inputPassword: String,
    val historyUsers: List<UserInfo>,
    val isLoading: Boolean,
    val loadingMsg: String,
    val rememberPassword: Boolean,
) {
    companion object {
        val Initial = LoginUiState(
            inputUsername = "",
            inputPassword = "",
            historyUsers = emptyList(),
            isLoading = false,
            loadingMsg = "",
            rememberPassword = false,
        )
    }
}

sealed interface LoginSideEffect {
    data class ShowToast(val msg: String) : LoginSideEffect
    data object NavigateToHome : LoginSideEffect
}

class LoginViewModel(
    private val userRepository: UserRepository,
    private val projectRepository: ProjectRepository,
    private val settingsRepository: SettingsRepository,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(LoginUiState.Initial)
    val uiState: StateFlow<LoginUiState> = _uiState.asStateFlow()
    private val _sideEffect = MutableSharedFlow<LoginSideEffect>()
    val sideEffect: SharedFlow<LoginSideEffect> = _sideEffect.asSharedFlow()

    init {
        viewModelScope.launch {
            combine(
                userRepository.historyUsersFlow(),
                settingsRepository.rememberPasswordFlow()
            ) { users, rememberPassword ->
                val lastLoggedInUser = users.firstOrNull()
                val shouldAutoFill =
                    lastLoggedInUser != null && _uiState.value.inputUsername.isEmpty() && _uiState.value.inputPassword.isEmpty()
                _uiState.update {
                    it.copy(
                        historyUsers = users,
                        rememberPassword = rememberPassword,
                        inputUsername = if (shouldAutoFill) lastLoggedInUser.username else it.inputUsername,
                        inputPassword = if (shouldAutoFill) lastLoggedInUser.password else it.inputPassword,
                    )
                }
            }.collect()
        }
    }

    fun onUsernameChanged(username: String) {
        _uiState.update { it.copy(inputUsername = username) }
    }

    fun onPasswordChanged(password: String) {
        _uiState.update { it.copy(inputPassword = password) }
    }

    fun handleUserLogin() {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true, loadingMsg = "登录中") }
                val loginResult =
                    userRepository.login(_uiState.value.inputUsername, _uiState.value.inputPassword)
                if (loginResult.isFailure) {
                    _uiState.update { it.copy(isLoading = false) }
                    _sideEffect.emit(LoginSideEffect.ShowToast("登录失败: ${loginResult.exceptionOrNull()?.message}"))
                    return@launch
                }
                _uiState.update { it.copy(isLoading = true, loadingMsg = "查询项目信息中") }
                val projectResult = projectRepository.queryProjectInfoFromNetwork()
                _uiState.update { it.copy(isLoading = false) }
                if (projectResult.isFailure) {
                    _sideEffect.emit(LoginSideEffect.ShowToast("查询项目信息失败: ${projectResult.exceptionOrNull()?.message}"))
                    return@launch
                }

                val (projectId, towerCraneId) = combine(
                    settingsRepository.projectIdFlow(),
                    settingsRepository.towerCraneIdFlow()
                ) { pId, tId -> pId to tId }.first()

                val networkProjectList = projectResult.getOrNull() ?: emptyList()
                val matchedResult = networkProjectList.firstNotNullOfOrNull { project ->
                    if (project.projectId == projectId) {
                        project.towerList.find { it.towerId == towerCraneId }
                            ?.let { tower -> project to tower }
                    } else {
                        null
                    }
                }
                if (matchedResult != null) {
                    userRepository.saveUser(loginResult.getOrThrow())
                    projectRepository.saveProjectAndTowerCranInfo(
                        matchedResult.first,
                        matchedResult.second
                    )
                    _sideEffect.emit(LoginSideEffect.NavigateToHome)
                } else {
                    _sideEffect.emit(LoginSideEffect.ShowToast("登录失败: 用户与当前项目不匹配"))
                }
            } catch (e: Exception) {
                logger.error("handleUserLogin exception", e)
            } finally {
                _uiState.update { it.copy(isLoading = false, loadingMsg = "") }
            }
        }
    }

    fun deleteUser(userInfo: UserInfo) {
        viewModelScope.launch {
            userRepository.deleteUserLoginHistory(userInfo)
        }
    }

    fun setRememberPassword(remember: Boolean) {
        viewModelScope.launch {
            settingsRepository.setRememberPassword(remember)
        }
    }
}
