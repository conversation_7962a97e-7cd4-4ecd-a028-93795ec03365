package com.fjdynamics.towermanager.ui

import androidx.lifecycle.viewModelScope
import com.fjdynamics.towermanager.MainNavRoutes
import com.fjdynamics.towermanager.data.repository.UserRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

data class MainUiState(
    val isLoading: Boolean = true,
    val startNavRoute: MainNavRoutes = MainNavRoutes.Home,
)

class MainViewModel(
    private val userRepository: UserRepository,
) : BaseViewModel() {
    private val _uiState = MutableStateFlow(MainUiState())
    val uiState = _uiState.asStateFlow()

    init {
        checkLoginStatus()
    }

    private fun checkLoginStatus() {
        viewModelScope.launch {
            val loggedInUser = userRepository.loggedInUserFlow().first()
            _uiState.update {
                it.copy(
                    isLoading = false,
                    startNavRoute = if (loggedInUser == null) MainNavRoutes.Login else MainNavRoutes.Home
                )
            }
        }
    }

}