package com.fjdynamics.towermanager.ui.home

import androidx.lifecycle.viewModelScope
import com.fjdynamics.towermanager.data.models.ProjectInfo
import com.fjdynamics.towermanager.data.models.TowerCraneInfo
import com.fjdynamics.towermanager.data.models.UserInfo
import com.fjdynamics.towermanager.data.network.websocket.WebsocketManager
import com.fjdynamics.towermanager.data.repository.ProjectRepository
import com.fjdynamics.towermanager.data.repository.UserRepository
import com.fjdynamics.towermanager.ui.BaseViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

data class HomeUiState(
    val userInfo: UserInfo? = null,
    val projectInfo: ProjectInfo? = null,
    val towerCraneInfo: TowerCraneInfo? = null,
)

class HomeViewModel(
    private val userRepository: UserRepository,
    private val projectRepository: ProjectRepository,
    private val websocketManager: WebsocketManager,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(HomeUiState())
    val uiState = _uiState.asStateFlow()

    init {
        viewModelScope.launch {
            combine(
                userRepository.loggedInUserFlow(),
                projectRepository.currProjectFlow(),
                projectRepository.currTowerCraneFlow()
            ) { user, project, towerCrane ->
                _uiState.update {
                    it.copy(
                        userInfo = user,
                        projectInfo = project,
                        towerCraneInfo = towerCrane
                    )
                }
            }.collect()
        }

        viewModelScope.launch {
            userRepository.updateUserDetailInfo()
        }

        viewModelScope.launch {
            websocketManager.controlMsgFlow.collect {
                logger.debug("control msg: {}", it)
            }
        }
    }

}