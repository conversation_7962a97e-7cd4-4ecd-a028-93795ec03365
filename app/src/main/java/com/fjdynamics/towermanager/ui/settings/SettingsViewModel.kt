package com.fjdynamics.towermanager.ui.settings

import androidx.lifecycle.viewModelScope
import com.fjdynamics.towermanager.data.repository.SettingsRepository
import com.fjdynamics.towermanager.ui.BaseViewModel
import com.fjdynamics.towermanager.utils.combine
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import okhttp3.internal.toLongOrDefault

data class GeneralSettingsUiState(
    val projectId: String = "",
    val towerCraneId: String = "",
)

data class FaceDetectionUiState(
    val interval: String = "",
    val lockScreen: Boolean = false,
)

data class VolumeUiState(
    val alarmVolume: Int = 0,
    val backgroundVolume: Int = 0,
)

class SettingsViewModel(
    private val settingsRepository: SettingsRepository,
) : BaseViewModel() {
    private val _generalUiState = MutableStateFlow(GeneralSettingsUiState())
    val generalUiState = _generalUiState.asStateFlow()
    private val _faceDetectionUiState = MutableStateFlow(FaceDetectionUiState())
    val faceDetectionUiState = _faceDetectionUiState.asStateFlow()
    private val _volumeUiState = MutableStateFlow(VolumeUiState())
    val volumeUiState = _volumeUiState.asStateFlow()

    private val _saveSuccessEvent = MutableSharedFlow<Unit>()
    val saveSuccessEvent: SharedFlow<Unit> = _saveSuccessEvent.asSharedFlow()

    init {
        viewModelScope.launch {
            combine(
                settingsRepository.projectIdFlow(),
                settingsRepository.towerCraneIdFlow(),
                settingsRepository.faceDetectionIntervalFlow(),
                settingsRepository.faceDetectionLockScreenFlow(),
                settingsRepository.alarmVolumeFlow(),
                settingsRepository.backgroundVolumeFlow(),
            ) { projectId, towerCraneId, faceDetectionInterval, faceDetectionLockScreen, alarmVolume, backgroundVolume ->
                _generalUiState.update {
                    it.copy(projectId.toString(), towerCraneId.toString())
                }
                _faceDetectionUiState.update {
                    it.copy(faceDetectionInterval.toString(), faceDetectionLockScreen)
                }
                _volumeUiState.update {
                    it.copy(alarmVolume, backgroundVolume)
                }
            }.collect()
        }
    }

    fun onProjectIdChanged(projectId: String) {
        _generalUiState.update { it.copy(projectId = projectId) }
    }

    fun onTowerCraneIdChanged(towerCraneId: String) {
        _generalUiState.update { it.copy(towerCraneId = towerCraneId) }
    }

    fun onFaceDetectionIntervalChanged(interval: String) {
        _faceDetectionUiState.update { it.copy(interval = interval) }
    }

    fun onFaceDetectionLockScreenChanged(checked: Boolean) {
        _faceDetectionUiState.update { it.copy(lockScreen = checked) }
    }

    fun onAlarmVolumeChanged(volume: Int) {
        _volumeUiState.update { it.copy(alarmVolume = volume) }
        viewModelScope.launch {
            settingsRepository.setAlarmVolume(volume)
        }
    }

    fun onBackgroundVolumeChanged(volume: Int) {
        _volumeUiState.update { it.copy(backgroundVolume = volume) }
        viewModelScope.launch {
            settingsRepository.setBackgroundVolume(volume)
        }
    }

    fun saveGeneralSettings() {
        viewModelScope.launch {
            settingsRepository.setGeneralSettings(
                _generalUiState.value.projectId.toLongOrDefault(0L),
                _generalUiState.value.towerCraneId.toLongOrDefault(0L)
            )
            _saveSuccessEvent.emit(Unit)
        }
    }

    fun saveFaceDetectionSettings() {
        viewModelScope.launch {
            settingsRepository.setFaceDetectionSettings(
                _faceDetectionUiState.value.interval.toIntOrNull() ?: 10,
                _faceDetectionUiState.value.lockScreen
            )
            _saveSuccessEvent.emit(Unit)
        }
    }
}