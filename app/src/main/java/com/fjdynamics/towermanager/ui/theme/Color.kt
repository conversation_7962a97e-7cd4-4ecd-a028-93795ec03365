package com.fjdynamics.towermanager.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

val AppPrimaryColor = Color(0xFF0091FA)

val ScreenBackgroundColor = Color(0xFFF5F5FA)

val TextPrimaryColor = Color(0xFF191923)
val TextSecondaryColor = Color(0x99191923)
val TextHintColor = Color(0x33191923)

val BorderColor = Color(0x1A191923)

val DividerColor = Color(0x1A191923)

val ButtonBackgroundColor = Color(0xFF0091FA)
val ButtonDisabledBackgroundColor = Color(0x660091FA)
val ButtonRedBackground = Color(0xFFFF4B50)

val CheckedTrackColor = Color(0xFF28CD78)

val InactiveTrackColor = Color(0xFFE6E6E6)
