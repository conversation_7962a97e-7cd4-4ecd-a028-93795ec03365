package com.fjdynamics.towermanager.ui.usercenter

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.navigation3.rememberViewModelStoreNavEntryDecorator
import androidx.navigation3.runtime.entry
import androidx.navigation3.runtime.entryProvider
import androidx.navigation3.runtime.rememberNavBackStack
import androidx.navigation3.runtime.rememberSavedStateNavEntryDecorator
import androidx.navigation3.ui.NavDisplay
import androidx.navigation3.ui.rememberSceneSetupNavEntryDecorator
import com.fjdynamics.towermanager.R
import com.fjdynamics.towermanager.UserCenterNavRoutes
import com.fjdynamics.towermanager.ui.theme.BorderColor
import com.fjdynamics.towermanager.ui.theme.ButtonRedBackground
import com.fjdynamics.towermanager.ui.theme.DividerColor
import com.fjdynamics.towermanager.ui.theme.TextPrimaryColor
import com.fjdynamics.towermanager.widgets.NavigationItem
import com.fjdynamics.towermanager.widgets.TextWithLabel
import com.fjdynamics.towermanager.widgets.TitleBar
import org.koin.androidx.compose.koinViewModel

private val USER_CENTER_NAV_ROUTES: List<UserCenterNavRoutes> = listOf(
    UserCenterNavRoutes.User,
    UserCenterNavRoutes.Work,
)

@Composable
fun UserCenterScreen(
    onBack: () -> Unit,
    onLogout: () -> Unit,
    viewModel: UserCenterViewModel = koinViewModel(),
) {

    LaunchedEffect(Unit) {
        viewModel.sideEffect.collect { sideEffect ->
            when (sideEffect) {
                is UserCenterSideEffect.UserLogout -> onLogout()
            }
        }
    }

    Column(modifier = Modifier.fillMaxSize()) {
        TitleBar("个人中心", onBackClick = onBack)

        val navBackStack = rememberNavBackStack(UserCenterNavRoutes.User)
        Row(modifier = Modifier.fillMaxWidth()) {
            Column(
                modifier = Modifier
                    .width(240.dp)
                    .fillMaxHeight()
                    .background(Color.White)
            ) {
                USER_CENTER_NAV_ROUTES.forEach { route ->
                    val isSelected = route == navBackStack.lastOrNull()
                    val onNavItemClick = {
                        if (!isSelected) {
                            navBackStack.remove(route)
                            navBackStack.add(route)
                        }
                    }

                    when (route) {
                        UserCenterNavRoutes.User -> {
                            NavigationItem(
                                icon = R.drawable.ic_user,
                                title = "个人资料",
                                onClick = onNavItemClick,
                                selected = isSelected,
                            )
                        }

                        UserCenterNavRoutes.Work -> {
                            NavigationItem(
                                icon = R.drawable.ic_personal_tower,
                                title = "工作塔吊",
                                onClick = onNavItemClick,
                                selected = isSelected,
                            )
                        }
                    }
                }
            }

            NavDisplay(
                entryDecorators = listOf(
                    rememberSceneSetupNavEntryDecorator(),
                    rememberSavedStateNavEntryDecorator(),
                    rememberViewModelStoreNavEntryDecorator()
                ),
                backStack = navBackStack,
                onBack = { },
                entryProvider = entryProvider {
                    entry<UserCenterNavRoutes.User> {
                        UserScreen(viewModel)
                    }
                    entry<UserCenterNavRoutes.Work> {
                        WorkScreen(viewModel)
                    }
                },
            )
        }
    }
}

@Composable
fun UserScreen(viewModel: UserCenterViewModel) {
    Column(
        modifier = Modifier
            .verticalScroll(rememberScrollState())
            .padding(horizontal = 21.dp, vertical = 14.dp),
    ) {
        val uiState = viewModel.uiState.collectAsStateWithLifecycle()

        OutlinedCard(
            shape = RoundedCornerShape(5.dp),
            colors = CardDefaults.outlinedCardColors(containerColor = Color.White),
            border = BorderStroke(1.dp, BorderColor)
        ) {
            val userInfo = uiState.value.userInfo
            val userInfoList = remember(userInfo) {
                listOf(
                    "帐号" to (userInfo?.username ?: ""),
                    "姓名" to (userInfo?.name ?: ""),
                    "岗位" to (userInfo?.positionName ?: ""),
                    "邮箱" to (userInfo?.email ?: ""),
                    "手机号码" to (userInfo?.phoneNo ?: "")
                )
            }
            userInfoList.forEachIndexed { index, (label, value) ->
                TextWithLabel(label, value)
                if (index < userInfoList.lastIndex) {
                    HorizontalDivider(thickness = 1.dp, color = DividerColor)
                }
            }
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 21.dp)
        ) {
            Button(
                onClick = { },
                shape = RoundedCornerShape(2.dp),
                colors = ButtonDefaults.buttonColors(containerColor = Color.White),
                modifier = Modifier
                    .weight(1f)
                    .height(58.dp),
                border = BorderStroke(1.dp, BorderColor)
            ) {
                Text(
                    "更改密码",
                    color = TextPrimaryColor,
                    fontSize = 21.sp,
                    fontWeight = FontWeight.Bold
                )
            }

            Spacer(Modifier.size(43.dp))

            Button(
                onClick = { viewModel.logout() },
                shape = RoundedCornerShape(2.dp),
                colors = ButtonDefaults.buttonColors(containerColor = ButtonRedBackground),
                modifier = Modifier
                    .weight(1f)
                    .height(58.dp)
            ) {
                Text(
                    "退出登录",
                    color = Color.White,
                    fontSize = 21.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
    }
}

@Composable
fun WorkScreen(viewModel: UserCenterViewModel) {
    Column(
        modifier = Modifier
            .verticalScroll(rememberScrollState())
            .padding(horizontal = 21.dp, vertical = 14.dp),
    ) {
        val uiState = viewModel.uiState.collectAsStateWithLifecycle()

        OutlinedCard(
            shape = RoundedCornerShape(5.dp),
            colors = CardDefaults.outlinedCardColors(containerColor = Color.White),
            border = BorderStroke(1.dp, BorderColor)
        ) {
            val projectInfo = uiState.value.projectInfo
            val towerCraneInfo = uiState.value.towerCraneInfo
            val workInfoList = remember(projectInfo, towerCraneInfo) {
                listOf(
                    "项目" to (projectInfo?.projectName ?: ""),
                    "塔吊" to (towerCraneInfo?.name ?: ""),
                    "塔吊品牌" to (towerCraneInfo?.brand ?: ""),
                    "塔吊型号" to (towerCraneInfo?.model ?: "")
                )
            }
            workInfoList.forEachIndexed { index, (label, value) ->
                TextWithLabel(label, value)
                if (index < workInfoList.lastIndex) {
                    HorizontalDivider(thickness = 1.dp, color = DividerColor)
                }
            }
        }
    }
}

