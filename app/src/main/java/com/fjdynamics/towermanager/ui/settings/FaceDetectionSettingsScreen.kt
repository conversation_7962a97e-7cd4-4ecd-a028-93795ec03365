package com.fjdynamics.towermanager.ui.settings

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.fjdynamics.towermanager.ui.theme.BorderColor
import com.fjdynamics.towermanager.ui.theme.ButtonBackgroundColor
import com.fjdynamics.towermanager.ui.theme.CheckedTrackColor
import com.fjdynamics.towermanager.ui.theme.TextPrimaryColor

@Composable
fun FaceDetectionSettingsScreen(viewModel: SettingsViewModel) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.padding(horizontal = 21.dp, vertical = 14.dp)
    ) {
        val uiState by viewModel.faceDetectionUiState.collectAsStateWithLifecycle()

        SettingsInputRow(
            modifier = Modifier
                .background(Color.White, RoundedCornerShape(5.dp))
                .border(
                    border = BorderStroke(1.dp, BorderColor),
                    shape = RoundedCornerShape(5.dp)
                ),
            label = "人脸认证间隔",
            value = uiState.interval,
            onValueChange = { newValue ->
                if (newValue.all { it.isDigit() }) {
                    viewModel.onFaceDetectionIntervalChanged(newValue)
                }
            },
        )

        Spacer(modifier = Modifier.size(21.dp))

        Row(
            modifier = Modifier
                .background(Color.White, RoundedCornerShape(5.dp))
                .border(
                    border = BorderStroke(1.dp, BorderColor),
                    shape = RoundedCornerShape(5.dp)
                )
                .padding(horizontal = 21.dp)
                .height(58.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = "认证失败是否锁屏",
                color = TextPrimaryColor,
                fontSize = 21.sp
            )
            Spacer(modifier = Modifier.weight(1f))
            Switch(
                checked = uiState.lockScreen,
                onCheckedChange = { viewModel.onFaceDetectionLockScreenChanged(it) },
                colors = SwitchDefaults.colors(
                    checkedThumbColor = Color.White,
                    checkedTrackColor = CheckedTrackColor,
                )
            )
        }

        Spacer(modifier = Modifier.size(45.dp))

        Button(
            onClick = { viewModel.saveFaceDetectionSettings() },
            shape = RoundedCornerShape(5.dp),
            colors = ButtonDefaults.buttonColors(containerColor = ButtonBackgroundColor),
            modifier = Modifier
                .fillMaxWidth()
                .height(58.dp)
        ) {
            Text("确定", color = Color.White, fontSize = 21.sp, fontWeight = FontWeight.Bold)
        }

    }
}