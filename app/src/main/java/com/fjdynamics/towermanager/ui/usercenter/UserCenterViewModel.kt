package com.fjdynamics.towermanager.ui.usercenter

import androidx.lifecycle.viewModelScope
import com.fjdynamics.towermanager.data.models.ProjectInfo
import com.fjdynamics.towermanager.data.models.TowerCraneInfo
import com.fjdynamics.towermanager.data.models.UserInfo
import com.fjdynamics.towermanager.data.repository.ProjectRepository
import com.fjdynamics.towermanager.data.repository.UserRepository
import com.fjdynamics.towermanager.ui.BaseViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

data class UserCenterUiState(
    val userInfo: UserInfo? = null,
    val projectInfo: ProjectInfo? = null,
    val towerCraneInfo: TowerCraneInfo? = null,
)

sealed interface UserCenterSideEffect {
    data object UserLogout : UserCenterSideEffect
}

class UserCenterViewModel(
    private val userRepository: UserRepository,
    private val projectRepository: ProjectRepository,
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(UserCenterUiState())
    val uiState = _uiState.asStateFlow()
    private val _sideEffect = MutableSharedFlow<UserCenterSideEffect>()
    val sideEffect = _sideEffect.asSharedFlow()

    init {
        viewModelScope.launch {
            combine(
                userRepository.loggedInUserFlow(),
                projectRepository.currProjectFlow(),
                projectRepository.currTowerCraneFlow()
            ) { user, project, towerCrane ->
                _uiState.update {
                    it.copy(
                        userInfo = user,
                        projectInfo = project,
                        towerCraneInfo = towerCrane
                    )
                }
            }.collect()
        }
    }

    fun logout() {
        viewModelScope.launch {
            userRepository.clearUserInfo()
            _sideEffect.emit(UserCenterSideEffect.UserLogout)
        }
    }

}