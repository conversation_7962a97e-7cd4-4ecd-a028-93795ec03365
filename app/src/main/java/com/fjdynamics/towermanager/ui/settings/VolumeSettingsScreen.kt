package com.fjdynamics.towermanager.ui.settings

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Slider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.fjdynamics.towermanager.ui.theme.TextPrimaryColor

@Composable
fun VolumeSettingsScreen(viewModel: SettingsViewModel) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.padding(horizontal = 21.dp, vertical = 14.dp)
    ) {
        val uiState by viewModel.volumeUiState.collectAsStateWithLifecycle()

        SliderWithTitle(
            title = "告警音量",
            value = uiState.alarmVolume.toFloat(),
            onValueChanged = { viewModel.onAlarmVolumeChanged(it.toInt()) },
        )

        Spacer(Modifier.size(32.dp))

        SliderWithTitle(
            title = "背景音量",
            value = uiState.backgroundVolume.toFloat(),
            onValueChanged = { viewModel.onBackgroundVolumeChanged(it.toInt()) },
        )
    }
}

@Composable
fun SliderWithTitle(
    title: String,
    value: Float,
    onValueChanged: (Float) -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        var sliderPosition by remember { mutableFloatStateOf(value) }
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = title,
                color = TextPrimaryColor,
                fontSize = 21.sp,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.weight(1f))
            Text(
                text = sliderPosition.toInt().toString(),
                color = TextPrimaryColor,
                fontSize = 21.sp,
                fontWeight = FontWeight.Bold
            )
        }
        Spacer(modifier = Modifier.size(10.dp))
        Slider(
            value = sliderPosition,
            onValueChange = { sliderPosition = it },
            onValueChangeFinished = { onValueChanged(sliderPosition) },
            valueRange = 0f..10f,
            steps = 9,
        )
    }
}
