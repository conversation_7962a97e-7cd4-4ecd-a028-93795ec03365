package com.fjdynamics.towermanager.ui.settings

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.fjdynamics.towermanager.ui.theme.BorderColor
import com.fjdynamics.towermanager.ui.theme.ButtonBackgroundColor
import com.fjdynamics.towermanager.ui.theme.DividerColor
import com.fjdynamics.towermanager.ui.theme.TextPrimaryColor

@Composable
fun GeneralSettingsScreen(
    viewModel: SettingsViewModel
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.padding(horizontal = 21.dp, vertical = 14.dp)
    ) {
        val uiState by viewModel.generalUiState.collectAsStateWithLifecycle()

        OutlinedCard(
            shape = RoundedCornerShape(5.dp),
            colors = CardDefaults.outlinedCardColors(containerColor = Color.White),
            border = BorderStroke(1.dp, BorderColor),
        ) {
            SettingsInputRow(
                label = "项目ID",
                value = uiState.projectId,
                onValueChange = { newValue ->
                    if (newValue.all { it.isDigit() }) {
                        viewModel.onProjectIdChanged(newValue)
                    }
                }
            )
            HorizontalDivider(thickness = 1.dp, color = DividerColor)
            SettingsInputRow(
                label = "塔吊ID",
                value = uiState.towerCraneId,
                onValueChange = { newValue ->
                    if (newValue.all { it.isDigit() }) {
                        viewModel.onTowerCraneIdChanged(newValue)
                    }
                }
            )
        }

        Spacer(modifier = Modifier.size(45.dp))

        Button(
            onClick = { viewModel.saveGeneralSettings() },
            shape = RoundedCornerShape(5.dp),
            colors = ButtonDefaults.buttonColors(containerColor = ButtonBackgroundColor),
            modifier = Modifier
                .fillMaxWidth()
                .height(58.dp)
        ) {
            Text("确定", color = Color.White, fontSize = 21.sp, fontWeight = FontWeight.Bold)
        }
    }
}

@Composable
fun SettingsInputRow(
    label: String,
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .padding(horizontal = 21.dp)
            .height(58.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Text(
            text = label,
            color = TextPrimaryColor,
            fontSize = 21.sp
        )
        Spacer(modifier = Modifier.weight(1f))
        BasicTextField(
            value = value,
            onValueChange = onValueChange,
            singleLine = true,
            textStyle = TextStyle(
                color = TextPrimaryColor,
                fontSize = 21.sp,
                textAlign = TextAlign.End,
            ),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number)
        )
    }
}