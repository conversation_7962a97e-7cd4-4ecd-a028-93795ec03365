package com.fjdynamics.towermanager

import androidx.navigation3.runtime.NavKey
import kotlinx.serialization.Serializable

@Serializable
sealed class MainNavRoutes : Nav<PERSON>ey {
    @Serializable
    data object Login : MainNavRoutes()

    @Serializable
    data object Home : MainNavRoutes()

    @Serializable
    data object Settings : MainNavRoutes()

    @Serializable
    data object UserCenter : MainNavRoutes()
}

@Serializable
sealed class SettingsNavRoutes : NavKey {
    @Serializable
    data object General : SettingsNavRoutes()

    @Serializable
    data object FaceDetection : SettingsNavRoutes()

    @Serializable
    data object Volume : SettingsNavRoutes()
}

@Serializable
sealed class UserCenterNavRoutes : NavKey {
    @Serializable
    object User : UserCenterNavRoutes()

    @Serializable
    object Work : UserCenterNavRoutes()
}