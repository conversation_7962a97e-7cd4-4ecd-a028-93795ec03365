package com.fjdynamics.towermanager

import android.app.Application
import android.text.TextUtils
import com.fjdynamics.towermanager.di.getAppModules
import com.fjdynamics.towermanager.utils.LoggingConfigurator
import com.fjdynamics.towermanager.utils.logger
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import org.koin.android.ext.koin.androidContext
import org.koin.core.context.startKoin

class TowerCranePadApp : Application() {

    override fun onCreate() {
        super.onCreate()

        LoggingConfigurator.configure(filesDir.resolve("logs").absolutePath)

        val scope = createAppRootCoroutineScope()

        startKoin {
            androidContext(this@TowerCranePadApp)
            modules(getAppModules(scope))
        }

    }

    companion object {
        val IS_PROD: Boolean = TextUtils.equals(BuildConfig.FLAVOR, "prod")
    }
}

fun createAppRootCoroutineScope(): CoroutineScope {
    val logger = logger("app-root")
    return CoroutineScope(
        CoroutineExceptionHandler { coroutineContext, throwable ->
            logger.warn("Uncaught exception in coroutine $coroutineContext", throwable)
        } + SupervisorJob() + Dispatchers.Default,
    )
}