package com.fjdynamics.towermanager.di

import com.fjdynamics.towermanager.data.network.PlatformApi
import com.fjdynamics.towermanager.data.persistent.database.AppDatabase
import com.fjdynamics.towermanager.data.persistent.projectDatastore
import com.fjdynamics.towermanager.data.persistent.settingsDatastore
import com.fjdynamics.towermanager.data.persistent.userDatastore
import com.fjdynamics.towermanager.data.repository.ProjectRepository
import com.fjdynamics.towermanager.data.repository.SettingsRepository
import com.fjdynamics.towermanager.data.repository.UserRepository
import kotlinx.serialization.json.Json
import org.koin.android.ext.koin.androidContext
import org.koin.core.scope.Scope
import org.koin.dsl.module


val Scope.database get() = get<AppDatabase>()

fun repositoryModule() = module {
    single<UserRepository> {
        UserRepository(
            database.userDao(),
            get<PlatformApi>(),
            androidContext().userDatastore,
            get<Json>(),
            get<SettingsRepository>()
        )
    }
    single<ProjectRepository> {
        ProjectRepository(
            get<PlatformApi>(),
            androidContext().projectDatastore,
            get<Json>()
        )
    }
    single<SettingsRepository> {
        SettingsRepository(androidContext().settingsDatastore)
    }
}
