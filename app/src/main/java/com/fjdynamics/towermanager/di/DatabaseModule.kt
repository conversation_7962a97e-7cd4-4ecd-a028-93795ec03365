package com.fjdynamics.towermanager.di

import androidx.room.Room.databaseBuilder
import com.fjdynamics.towermanager.data.persistent.database.AppDatabase
import com.fjdynamics.towermanager.data.persistent.database.dao.UserDao
import org.koin.android.ext.koin.androidContext
import org.koin.dsl.module

fun databaseModule() = module {
    single<AppDatabase> {
        databaseBuilder(androidContext(), AppDatabase::class.java, "towermanager.db")
            .fallbackToDestructiveMigrationOnDowngrade(true)
            .build()
    }
    single<UserDao> {
        get<AppDatabase>().userDao()
    }
}