package com.fjdynamics.towermanager.data.network

import com.fjdynamics.towermanager.data.network.models.PlatformApiResponse
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.post
import io.ktor.client.request.setBody

interface FaceDetectionApi {

    suspend fun updateEmpId(empId: Long): Boolean

    suspend fun queryDetectionResult(empId: Long): Boolean
}

class FaceDetectionApiImpl(
    private val httpClient: HttpClient,
    private val baseUrl: String = "http://172.16.7.56:9200",
) : FaceDetection<PERSON>pi {
    override suspend fun updateEmpId(empId: Long): Boolean {
        val response = httpClient.post("$baseUrl/api/face_recognition/start") {
            setBody(mapOf("empId" to empId))
        }.body<PlatformApiResponse<Any>>()
        return response.success
    }

    override suspend fun queryDetectionResult(empId: Long): <PERSON><PERSON><PERSON> {
        val response = httpClient.post("$baseUrl/api/face_recognition/result") {
            setBody(mapOf("empId" to empId))
        }.body<PlatformApiResponse<Any>>()
        return response.success
    }

}