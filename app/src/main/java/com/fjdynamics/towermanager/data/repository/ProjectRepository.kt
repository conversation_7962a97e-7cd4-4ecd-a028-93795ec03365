package com.fjdynamics.towermanager.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import com.fjdynamics.towermanager.data.models.ProjectInfo
import com.fjdynamics.towermanager.data.models.TowerCraneInfo
import com.fjdynamics.towermanager.data.models.TowerCraneType
import com.fjdynamics.towermanager.data.network.PlatformApi
import com.fjdynamics.towermanager.data.network.models.NetworkProjectInfo
import com.fjdynamics.towermanager.data.network.models.NetworkTowerCraneInfo
import com.fjdynamics.towermanager.data.persistent.ProjectPreferenceKeys
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json

class ProjectRepository(
    private val platformApi: PlatformApi,
    private val projectDataStore: DataStore<Preferences>,
    private val json: Json,
) : Repository() {

    suspend fun queryProjectInfoFromNetwork(): Result<List<NetworkProjectInfo>> =
        withContext(ioDispatcher) {
            try {
                val projectList = platformApi.queryProjectInfo().projectList
                if (projectList.isEmpty()) {
                    logger.warn("queryProjectInfoFromNetwork: project list is empty")
                    Result.failure(RepositoryNetworkException("云平台绑定项目列表为空"))
                } else {
                    Result.success(projectList)
                }
            } catch (e: Exception) {
                logger.error("queryProjectInfoFromNetwork failed", e)
                Result.failure(e)
            }
        }

    fun currProjectFlow(): Flow<ProjectInfo?> = projectDataStore.data.map { preferences ->
        preferences[ProjectPreferenceKeys.PROJECT]?.let {
            json.decodeFromString(it)
        }
    }

    fun currTowerCraneFlow(): Flow<TowerCraneInfo?> = projectDataStore.data.map { preferences ->
        preferences[ProjectPreferenceKeys.TOWER_CRANE]?.let {
            json.decodeFromString(it)
        }
    }

    suspend fun saveProjectAndTowerCranInfo(
        networkProjectInfo: NetworkProjectInfo,
        networkTowerCraneInfo: NetworkTowerCraneInfo
    ) = withContext(Dispatchers.IO) {
        projectDataStore.edit { preferences ->
            preferences[ProjectPreferenceKeys.PROJECT] = json.encodeToString(
                ProjectInfo(
                    id = networkProjectInfo.projectId,
                    projectName = networkProjectInfo.projectName
                )
            )
            preferences[ProjectPreferenceKeys.TOWER_CRANE] = json.encodeToString(
                TowerCraneInfo(
                    id = networkTowerCraneInfo.towerId,
                    name = networkTowerCraneInfo.towerName,
                    brand = networkTowerCraneInfo.brand,
                    model = networkTowerCraneInfo.model,
                    type = TowerCraneType.fromDeviceType(networkTowerCraneInfo.deviceType),
                )
            )
        }
    }
}