package com.fjdynamics.towermanager.data.network

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import com.fjdynamics.towermanager.data.models.AuthInfo
import com.fjdynamics.towermanager.data.persistent.UserPreferenceKeys
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.serialization.json.Json

class AuthProvider(
    private val datastore: DataStore<Preferences>,
    private val json: Json,
) {

    fun authFlow(): Flow<AuthInfo?> = datastore.data.map { preferences ->
        preferences[UserPreferenceKeys.AUTH]?.let {
            json.decodeFromString(it)
        }
    }
}