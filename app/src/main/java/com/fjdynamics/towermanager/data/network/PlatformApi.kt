package com.fjdynamics.towermanager.data.network

import com.fjdynamics.towermanager.data.network.models.LoginResponse
import com.fjdynamics.towermanager.data.network.models.PlatformApiResponse
import com.fjdynamics.towermanager.data.network.models.ProjectResponse
import com.fjdynamics.towermanager.data.network.models.UserDetailResponse
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.contentType
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put

const val PLATFORM_API_HOST = "https://umtc-test.fjdynamics.com"

interface PlatformApi {

    suspend fun login(username: String, password: String): LoginResponse

    suspend fun queryProjectInfo(): ProjectResponse

    suspend fun queryUserDetail(): UserDetailResponse
}

class PlatformApiImpl(
    private val httpClient: HttpClient,
    private val baseUrl: String = PLATFORM_API_HOST,
) : PlatformApi {

    override suspend fun login(username: String, password: String): LoginResponse {
        val response = httpClient.post("$baseUrl/api/login") {
            contentType(ContentType.Application.Json)
            setBody(
                buildJsonObject {
                    put("username", username)
                    put("password", password)
                    put("verifyType", "none")
                    put("clientId", "123456789")
                }
            )
        }.body<PlatformApiResponse<LoginResponse>>()

        if (!response.success || response.data == null) {
            throw ApiException.ServerException(response.msg)
        }
        return response.data
    }

    override suspend fun queryProjectInfo(): ProjectResponse {
        val response =
            httpClient.get("$baseUrl/api/tower/config").body<PlatformApiResponse<ProjectResponse>>()

        if (!response.success || response.data == null) {
            throw ApiException.ServerException(response.msg)
        }
        return response.data
    }

    override suspend fun queryUserDetail(): UserDetailResponse {
        val response =
            httpClient.get("$baseUrl/api/login/token").body<PlatformApiResponse<UserDetailResponse>>()

        if (!response.success || response.data == null) {
            throw ApiException.ServerException(response.msg)
        }
        return response.data
    }
}