package com.fjdynamics.towermanager.data.persistent.database.dao

import androidx.room.Dao
import androidx.room.Query
import androidx.room.Upsert
import com.fjdynamics.towermanager.data.persistent.database.entity.UserEntity
import kotlinx.coroutines.flow.Flow

@Dao
interface UserDao {

    @Query("SELECT * FROM user ORDER BY updateAt DESC")
    fun findAllUsersDesc(): Flow<List<UserEntity>>

    @Upsert
    suspend fun insert(user: UserEntity)

    @Query("DELETE FROM user WHERE username = :username")
    suspend fun deleteByUsername(username: String)
}