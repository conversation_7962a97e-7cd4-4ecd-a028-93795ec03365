package com.fjdynamics.towermanager.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import com.fjdynamics.towermanager.data.persistent.SettingsPreferenceKeys
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext

class SettingsRepository(
    private val settingsDataStore: DataStore<Preferences>
) : Repository() {

    fun rememberPasswordFlow(): Flow<Boolean> = settingsDataStore.data.map { preferences ->
        preferences[SettingsPreferenceKeys.REMEMBER_PASSWORD] ?: false
    }

    suspend fun setRememberPassword(remember: Boolean) = withContext(ioDispatcher) {
        settingsDataStore.edit { preferences ->
            preferences[SettingsPreferenceKeys.REMEMBER_PASSWORD] = remember
        }
    }

    fun projectIdFlow(): Flow<Long> = settingsDataStore.data.map { preferences ->
        preferences[SettingsPreferenceKeys.PROJECT_ID] ?: 47L
    }

    fun towerCraneIdFlow(): Flow<Long> = settingsDataStore.data.map { preferences ->
        preferences[SettingsPreferenceKeys.TOWER_CRANE_ID] ?: 38L
    }

    suspend fun setGeneralSettings(projectId: Long, towerCraneId: Long) =
        withContext(ioDispatcher) {
            settingsDataStore.edit { preferences ->
                preferences[SettingsPreferenceKeys.PROJECT_ID] = projectId
                preferences[SettingsPreferenceKeys.TOWER_CRANE_ID] = towerCraneId
            }
        }

    fun faceDetectionIntervalFlow(): Flow<Int> = settingsDataStore.data.map { preferences ->
        preferences[SettingsPreferenceKeys.FACE_DETECTION_INTERVAL] ?: 10
    }

    fun faceDetectionLockScreenFlow(): Flow<Boolean> = settingsDataStore.data.map { preferences ->
        preferences[SettingsPreferenceKeys.FACE_DETECTION_LOCK_SCREEN] ?: false
    }

    suspend fun setFaceDetectionSettings(interval: Int, lockScreen: Boolean) =
        withContext(ioDispatcher) {
            settingsDataStore.edit { preferences ->
                preferences[SettingsPreferenceKeys.FACE_DETECTION_INTERVAL] = interval
                preferences[SettingsPreferenceKeys.FACE_DETECTION_LOCK_SCREEN] = lockScreen
            }
        }

    fun alarmVolumeFlow(): Flow<Int> = settingsDataStore.data.map { preferences ->
        preferences[SettingsPreferenceKeys.ALARM_VOLUME] ?: 10
    }

    fun backgroundVolumeFlow(): Flow<Int> = settingsDataStore.data.map { preferences ->
        preferences[SettingsPreferenceKeys.BACKGROUND_VOLUME] ?: 10
    }

    suspend fun setAlarmVolume(alarmVolume: Int) = withContext(ioDispatcher) {
        settingsDataStore.edit { preferences ->
            preferences[SettingsPreferenceKeys.ALARM_VOLUME] = alarmVolume
        }
    }

    suspend fun setBackgroundVolume(backgroundVolume: Int) = withContext(ioDispatcher) {
        settingsDataStore.edit { preferences ->
            preferences[SettingsPreferenceKeys.BACKGROUND_VOLUME] = backgroundVolume
        }
    }
}
