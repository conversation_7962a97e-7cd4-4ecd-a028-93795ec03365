package com.fjdynamics.towermanager.data.models

import kotlinx.serialization.Serializable

@Serializable
data class TowerCraneInfo(
    val id: Long,
    val name: String,
    val brand: String,
    val model: String,
    val type: TowerCraneType,
)

@Serializable
enum class TowerCraneType {
    /**
     * 动臂塔吊
     */
    LUFFING_JIB,

    /**
     * 平头塔吊
     */
    FLAT_TOP;

    companion object {
        fun fromDeviceType(value: Int): TowerCraneType {
            return when (value) {
                1 -> LUFFING_JIB
                2 -> FLAT_TOP
                else -> LUFFING_JIB
            }
        }
    }
}

