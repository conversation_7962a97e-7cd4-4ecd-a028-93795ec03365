package com.fjdynamics.towermanager.data.persistent.database

import androidx.room.Database
import androidx.room.RoomDatabase
import com.fjdynamics.towermanager.data.persistent.database.dao.UserDao
import com.fjdynamics.towermanager.data.persistent.database.entity.UserEntity

@Database(
    version = 1,
    entities = [UserEntity::class]
)
abstract class AppDatabase : RoomDatabase() {

    abstract fun userDao(): UserDao
}