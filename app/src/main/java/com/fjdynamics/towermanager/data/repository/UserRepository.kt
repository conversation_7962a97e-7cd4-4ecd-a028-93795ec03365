package com.fjdynamics.towermanager.data.repository

import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import com.fjdynamics.towermanager.data.models.AuthInfo
import com.fjdynamics.towermanager.data.models.UserInfo
import com.fjdynamics.towermanager.data.network.FaceDetectionApi
import com.fjdynamics.towermanager.data.network.PlatformApi
import com.fjdynamics.towermanager.data.persistent.UserPreferenceKeys
import com.fjdynamics.towermanager.data.persistent.database.dao.UserDao
import com.fjdynamics.towermanager.data.persistent.database.entity.UserEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json

class UserRepository(
    private val historyUserDao: UserDao,
    private val platformApi: PlatformApi,
    private val userDataStore: DataStore<Preferences>,
    private val json: <PERSON>son,
    private val settingsRepository: SettingsRepository,
) : Repository() {

    fun historyUsersFlow(): Flow<List<UserInfo>> = historyUserDao.findAllUsersDesc()
        .map { userList ->
            userList.map { userEntity ->
                UserInfo(
                    uid = userEntity.id,
                    username = userEntity.username,
                    password = userEntity.password,
                )
            }
        }
        .flowOn(defaultDispatcher)

    fun loggedInUserFlow(): Flow<UserInfo?> = userDataStore.data.map { preferences ->
        preferences[UserPreferenceKeys.USER]?.let {
            json.decodeFromString(it)
        }
    }

    suspend fun login(username: String, password: String): Result<UserInfo> =
        withContext(ioDispatcher) {
            try {
                val response = platformApi.login(username, password)
                userDataStore.edit { preferences ->
                    preferences[UserPreferenceKeys.AUTH] = json.encodeToString(
                        AuthInfo(
                            response.authorization,
                            response.accessPlatformId,
                            response.accessOrgId
                        )
                    )
                }
                Result.success(
                    UserInfo(
                        uid = response.uid,
                        username = response.username,
                        password = password,
                    )
                )
            } catch (e: Exception) {
                logger.error("login failed", e)
                Result.failure(e)
            }
        }

    suspend fun saveUser(userInfo: UserInfo) = withContext(ioDispatcher) {
        val rememberPassword = settingsRepository.rememberPasswordFlow().first()
        historyUserDao.insert(userInfo.toEntity(rememberPassword))
        userDataStore.edit { preferences ->
            preferences[UserPreferenceKeys.USER] = json.encodeToString(
                UserInfo(
                    uid = userInfo.uid,
                    username = userInfo.username,
                )
            )
        }
    }

    suspend fun deleteUserLoginHistory(userInfo: UserInfo) = withContext(ioDispatcher) {
        historyUserDao.deleteByUsername(userInfo.username)
    }

    suspend fun clearUserInfo() = withContext(ioDispatcher) {
        userDataStore.edit { preferences ->
            preferences.remove(UserPreferenceKeys.AUTH)
            preferences.remove(UserPreferenceKeys.USER)
        }
    }

    suspend fun updateUserDetailInfo() = withContext(ioDispatcher) {
        try {
            val currentUser = loggedInUserFlow().first()
            if (currentUser == null) {
                logger.warn("updateUserDetailInfo: no user currently logged in")
                return@withContext
            }

            val userDetailResponse = platformApi.queryUserDetail()

            val updatedUserInfo = currentUser.copy(
                name = userDetailResponse.name,
                phoneNo = userDetailResponse.phoneNo,
                email = userDetailResponse.email,
                empId = userDetailResponse.empId,
                positionName = userDetailResponse.positionName,
            )

            userDataStore.edit { preferences ->
                preferences[UserPreferenceKeys.USER] = json.encodeToString(updatedUserInfo)
            }

            logger.debug("updateUserDetailInfo success: {}", updatedUserInfo)
        } catch (e: Exception) {
            logger.error("updateUserDetailInfo failed", e)
        }
    }
}

fun UserInfo.toEntity(rememberPassword: Boolean = false) = UserEntity(
    id = uid,
    username = username,
    password = if (rememberPassword) password else "",
    updateAt = System.currentTimeMillis()
)