package com.fjdynamics.towermanager.data.network.models

import kotlinx.serialization.Serializable

@Serializable
data class CentralConfig(
    val type: String,
    val projectId: String,
    val towerId: String,
    val cameras: List<CentralCameraInfo>,
)

@Serializable
data class CentralCameraInfo(
    val type: Int,
    val ip: String,
    val rtspPort: Int,
    val httpPort: Int,
    val user: String,
    val password: String,
    val rtspPath: String,
)
