package com.fjdynamics.towermanager.data.network.models

import kotlinx.serialization.Serializable

@Serializable
data class PlatformApiResponse<T>(
    val code: Int,
    val success: Boolean,
    val msg: String,
    val data: T?,
)

@Serializable
data class LoginResponse(
    val uid: Long,
    val username: String,
    val accessPlatformId: String,
    val accessOrgId: String,
    val authorization: String,
)

@Serializable
data class ProjectResponse(
    val projectList: List<NetworkProjectInfo>
)

@Serializable
data class NetworkProjectInfo(
    val projectId: Long,
    val projectName: String,
    val towerList: List<NetworkTowerCraneInfo>
)

@Serializable
data class NetworkTowerCraneInfo(
    val towerId: Long,
    val towerName: String,
    val brand: String,
    val model: String,
    val deviceType: Int,
)

@Serializable
data class UserDetailResponse(
    val uid: Long,
    val username: String,
    val name: String,
    val email: String,
    val phoneNo: String,
    val empId: Long,
    val positionName: String,
)

