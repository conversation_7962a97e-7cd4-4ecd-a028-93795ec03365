package com.fjdynamics.towermanager.data.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CameraInfo(
    val cameraName: String,
    @SerialName("intranetIp") val ip: String,
    val port: Int,
    val rtspPort: Int,
    @SerialName("loginName") val username: String,
    val password: String,
    val posType: Int,
    val rtspPath: String,
)
