package com.fjdynamics.towermanager.data.network.websocket

import com.fjdynamics.towermanager.data.network.FaceDetectionManager
import com.fjdynamics.towermanager.data.network.models.ButtonStatus
import com.fjdynamics.towermanager.data.network.models.CentralWsMsgPayload
import com.fjdynamics.towermanager.data.network.models.ControlWsMsgPayload
import com.fjdynamics.towermanager.data.network.models.TowerCraneStatus
import com.fjdynamics.towermanager.data.repository.UserRepository
import com.fjdynamics.towermanager.utils.thisLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.stateIn
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

class WebsocketManager(
    centralWsClient: CentralWsClient,
    controlWsClient: ControlWsClient,
    private val json: Json,
    scope: CoroutineScope,
    userRepository: UserRepository,
    faceDetectionManager: FaceDetectionManager,
) {
    private val logger = thisLogger()
    private val centralWsFlow = centralWsClient.wsConnectFlow
    private val controlWsFlow = controlWsClient.wsConnectFlow
    private var currCentralConnStatus = WebSocketEvent.Disconnected
    private var currControlConnStatus = WebSocketEvent.Disconnected

    init {
        combine(
            centralWsFlow,
            controlWsFlow,
            userRepository.loggedInUserFlow()
                .distinctUntilChanged { old, new -> old?.uid == new?.uid },
            faceDetectionManager.lockStatus,
        ) { centralEvent, controlEvent, loggedInUser, lockScreen ->
            logger.debug(
                "centralEvent: {}, controlEvent: {}, loggedInUser: {}, lockScreen: {}",
                centralEvent,
                controlEvent,
                loggedInUser,
                lockScreen
            )

        }.stateIn(
            scope = scope,
            started = SharingStarted.Eagerly,
            initialValue = null
        )
    }

    val controlMsgFlow: Flow<ControlWsMsgPayload> =
        controlWsFlow.filterIsInstance<WebSocketEvent.OnMessage>()
            .mapNotNull { parseWebsocketMessage(it.text) }
            .flowOn(Dispatchers.IO)

    val centralMsgFlow: Flow<CentralWsMsgPayload> =
        centralWsFlow.filterIsInstance<WebSocketEvent.OnMessage>()
            .mapNotNull { parseCentralWsMsg(it.text) }
            .flowOn(Dispatchers.IO)

    private fun parseWebsocketMessage(jsonStr: String): ControlWsMsgPayload? {
        try {
            val jsonObject = json.parseToJsonElement(jsonStr).jsonObject
            val msgType = jsonObject["messageType"]?.jsonPrimitive?.content
            val dataElement = jsonObject["data"] ?: return null
            return when (msgType) {
                "towerData" -> json.decodeFromJsonElement<TowerCraneStatus>(dataElement)
                "buttons" -> {
                    val buttonList = json.decodeFromJsonElement<List<Int>>(dataElement)
                    ButtonStatus(buttonList)
                }

                else -> {
                    logger.warn("Unknown message type: $msgType")
                    null
                }
            }
        } catch (e: Exception) {
            logger.warn("Failed to decode tower crane status", e)
            return null
        }
    }

    private fun parseCentralWsMsg(jsonStr: String): CentralWsMsgPayload? {
        return null
    }
}