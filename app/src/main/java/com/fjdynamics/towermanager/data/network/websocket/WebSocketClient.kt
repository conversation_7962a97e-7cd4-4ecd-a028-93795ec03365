package com.fjdynamics.towermanager.data.network.websocket

import com.fjdynamics.towermanager.TowerCranePadApp
import com.fjdynamics.towermanager.data.network.models.CentralConfig
import com.fjdynamics.towermanager.utils.thisLogger
import io.ktor.client.HttpClient
import io.ktor.client.plugins.websocket.DefaultClientWebSocketSession
import io.ktor.client.plugins.websocket.webSocketSession
import io.ktor.websocket.Frame
import io.ktor.websocket.close
import io.ktor.websocket.readText
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.retry
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.isActive
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import java.util.UUID
import kotlin.coroutines.cancellation.CancellationException

data class WebSocketConfig(
    val host: String,
    val port: Int,
    val path: String = "/",
    val reconnectDelay: Long = 5_000L,
)

sealed interface WebSocketEvent {
    object Connecting : WebSocketEvent
    object Connected : WebSocketEvent
    object Disconnected : WebSocketEvent
    data class OnMessage(val text: String) : WebSocketEvent
}

abstract class WebSocketClient(
    val connConfig: WebSocketConfig,
    private val httpClient: HttpClient,
    private val scope: CoroutineScope,
    val json: Json,
) {
    val logger = thisLogger()

    @Volatile
    private var session: DefaultClientWebSocketSession? = null

    val wsConnectFlow: SharedFlow<WebSocketEvent> = flow {
        emit(WebSocketEvent.Connecting)

        val session = httpClient.webSocketSession(
            host = connConfig.host,
            port = connConfig.port,
            path = connConfig.path
        )

        <EMAIL> = session
        emit(WebSocketEvent.Connected)
        logger.info("Connected to $connConfig")

        for (frame in session.incoming) {
            if (frame is Frame.Text) {
                emit(WebSocketEvent.OnMessage(frame.readText()))
            }
        }
    }.catch { cause ->
        if (cause !is CancellationException) {
            emit(WebSocketEvent.Disconnected)
        }
        throw cause
    }.retry { cause ->
        if (cause is CancellationException) {
            //Don't retry if the scope was cancelled
            false
        } else {
            logger.error(
                "Connection failed, will retry in ${connConfig.reconnectDelay / 1000}s",
                cause
            )
            delay(connConfig.reconnectDelay)
            true
        }
    }.onCompletion { cause ->
        logger.warn("Flow completed with cause: ${cause?.message}")
        session?.close()
        emit(WebSocketEvent.Disconnected)
    }.flowOn(Dispatchers.IO)
        .shareIn(
            scope = scope,
            started = SharingStarted.Eagerly,
            replay = 1,
        )

    suspend inline fun <reified T> sendData(data: T) {
        try {
            send(json.encodeToString(data))
        } catch (e: Exception) {
            logger.error("sendData failed", e)
        }
    }

    suspend fun send(text: String): Boolean {
        return session?.let {
            if (it.isActive) {
                it.send(Frame.Text(text))
                logger.debug("msg sent: $text")
                true
            } else false
        } ?: false
    }
}

class CentralWsClient(
    httpClient: HttpClient,
    scope: CoroutineScope,
    json: Json,
) : WebSocketClient(
    WebSocketConfig(
        "172.16.7.56",
        8070,
        "/monitor/mini_ctrl/${if (TowerCranePadApp.IS_PROD) "prod" else "test"}"
    ),
    httpClient,
    scope,
    json,
) {

    suspend fun sendConfig(config: CentralConfig) {
        sendData(config)
    }
}

class ControlWsClient(
    httpClient: HttpClient,
    scope: CoroutineScope,
    json: Json,
) : WebSocketClient(
    WebSocketConfig(
        "192.168.0.10",
        10086,
    ),
    httpClient,
    scope,
    json,
) {

    suspend fun notifyLockStatus(uid: Long, locked: Boolean) {
        val lockStatus = LockStatus(uid, if (locked) 0 else 1)
        val msg = WebSocketMsg(messageType = "loginStatusUpdate", data = lockStatus)
        sendData(msg)
    }

    @Serializable
    data class WebSocketMsg<T>(
        val messageType: String,
        val data: T,
        val messageId: String = UUID.randomUUID().toString().replace("-", ""),
        val timestamp: Long = System.currentTimeMillis(),
    )

    @Serializable
    data class LockStatus(val uid: Long, val locked: Int)
}