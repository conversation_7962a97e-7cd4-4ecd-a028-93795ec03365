package com.fjdynamics.towermanager.data.repository

sealed class RepositoryException : Exception {
    constructor() : super()
    constructor(message: String?) : super(message)
    constructor(message: String?, cause: Throwable?) : super(message, cause)
}

class RepositoryNetworkException(message: String? = null, cause: Throwable? = null) :
    RepositoryException(message, cause)

class RepositoryUnknownException(throwable: Throwable) :
    RepositoryException(null, throwable)