package com.fjdynamics.towermanager.data.network

import com.fjdynamics.towermanager.data.repository.SettingsRepository
import com.fjdynamics.towermanager.data.repository.UserRepository
import com.fjdynamics.towermanager.utils.thisLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.coroutines.cancellation.CancellationException

@OptIn(ExperimentalCoroutinesApi::class)
class FaceDetectionManager(
    private val faceDetectionApi: FaceDetectionApi,
    userRepository: UserRepository,
    settingsRepository: SettingsRepository,
    scope: CoroutineScope,
) {
    private val logger = thisLogger()
    private val _lockStatus = MutableStateFlow(false)
    val lockStatus: StateFlow<Boolean> = _lockStatus.asStateFlow()

    init {
        val configFlow = combine(
            userRepository.loggedInUserFlow()
                .distinctUntilChanged { old, new -> old?.empId == new?.empId },
            settingsRepository.faceDetectionLockScreenFlow(),
            settingsRepository.faceDetectionIntervalFlow(),
        ) { user, lockScreen, interval ->
            FaceDetectionConfig(user?.empId ?: 0L, interval, lockScreen)
        }

        scope.launch {
            configFlow.flatMapLatest { config ->
                if (config.empId != 0L) {
                    logger.info("User logged in, starting detection flow.")
                    createDetectionFlow(config.empId, config.interval)
                        .map { detectResult -> config.lockScreen && detectResult }
                } else {
                    logger.info("User logged out. Stopping detection flow.")
                    emptyFlow()
                }
            }.onCompletion {
                if (it is CancellationException) {
                    logger.debug("Detection flow cancelled. Resetting lock status.")
                    _lockStatus.value = false
                }
            }.catch { e ->
                if (e is CancellationException) throw e
                logger.error("Detection flow encountered an unrecoverable error.", e)
            }.collect { result ->
                handleFaceDetectionResult(result)
            }
        }
    }

    private fun createDetectionFlow(empId: Long, interval: Int): Flow<Boolean> = flow {
        //通知大屏后台更新empId
        while (currentCoroutineContext().isActive) {
            try {
                val result = faceDetectionApi.updateEmpId(empId)
                if (result) {
                    logger.debug("Successfully updated empId ($empId) to server.")
                    delay(2000)
                    break
                } else {
                    logger.error("Failed to notify empId. Retrying in 5s.")
                    delay(5000)
                }
            } catch (e: Exception) {
                if (e is CancellationException) throw e
                logger.error("Failed to notify server about empId. Retrying in 5s.", e)
                delay(5000)
            }
        }

        //查询人脸识别结果
        var faceErrorCount = 0
        while (currentCoroutineContext().isActive) {
            try {
                val result = faceDetectionApi.queryDetectionResult(empId)
                if (result) {
                    //人脸匹配成功
                    logger.debug("Face matched. Next check in {}s.", interval)
                    faceErrorCount = 0
                    emit(true)
                    delay(interval * 1_000L)
                } else {
                    //人脸匹配不成功
                    faceErrorCount++
                    logger.warn("Face did not match. Error count: $faceErrorCount")
                    if (faceErrorCount >= 5) {
                        // 连续5次失败，发出失败状态
                        emit(false)
                    }
                    delay(1_000L)
                }
            } catch (e: Exception) {
                if (e is CancellationException) throw e
                logger.error("Failed to query face detection result", e)
                delay(5_000L)
            }
        }
    }


    private fun handleFaceDetectionResult(lockStatus: Boolean) {
        _lockStatus.value = lockStatus
    }
}

data class FaceDetectionConfig(
    val empId: Long,
    val interval: Int,
    val lockScreen: Boolean,
)
