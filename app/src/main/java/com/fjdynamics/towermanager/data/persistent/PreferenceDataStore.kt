package com.fjdynamics.towermanager.data.persistent

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.longPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore


val Context.userDatastore: DataStore<Preferences> by preferencesDataStore("user")
val Context.settingsDatastore: DataStore<Preferences> by preferencesDataStore("settings")
val Context.projectDatastore: DataStore<Preferences> by preferencesDataStore("project")

object UserPreferenceKeys {
    //云平台接口鉴权信息
    val AUTH = stringPreferencesKey("auth")

    //当前登录用户信息
    val USER = stringPreferencesKey("user")
}

object ProjectPreferenceKeys {
    //当前项目信息
    val PROJECT = stringPreferencesKey("project")

    //当前塔吊信息
    val TOWER_CRANE = stringPreferencesKey("tower_crane")
}

object SettingsPreferenceKeys {
    //登录相关
    val REMEMBER_PASSWORD = booleanPreferencesKey("remember_password")

    //基本属性配置
    val PROJECT_ID = longPreferencesKey("project_id")
    val TOWER_CRANE_ID = longPreferencesKey("tower_crane_id")

    //人脸识别配置
    val FACE_DETECTION_INTERVAL = intPreferencesKey("face_detection_interval")
    val FACE_DETECTION_LOCK_SCREEN = booleanPreferencesKey("face_detection_lock_screen")

    //音量控制配置
    val ALARM_VOLUME = intPreferencesKey("alarm_volume")
    val BACKGROUND_VOLUME = intPreferencesKey("background_volume")
}