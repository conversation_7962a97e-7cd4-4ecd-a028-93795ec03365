package com.fjdynamics.towermanager.utils

import com.fjdynamics.towermanager.BuildConfig
import org.slf4j.ILoggerFactory
import org.slf4j.Logger
import org.slf4j.LoggerFactory

fun logger(name: String): Logger {
    return LoggerFactory.getILoggerFactory().getLogger(name)
}

@JvmName("logger1")
inline fun <reified T : Any> logger(): Logger {
    return LoggerFactory.getILoggerFactory().getLogger(T::class.java)
}

fun Any.thisLogger(): Logger {
    return LoggerFactory.getILoggerFactory().getLogger(this::class.java)
}

fun ILoggerFactory.getLogger(clazz: Class<out Any>): Logger =
    getLogger(
        clazz.canonicalName?.let {
            when {
                it.startsWith(BuildConfig.APPLICATION_ID) -> {
                    it.removePrefix(BuildConfig.APPLICATION_ID)
                }

                else -> {
                    it
                }
            }
        },
    )


