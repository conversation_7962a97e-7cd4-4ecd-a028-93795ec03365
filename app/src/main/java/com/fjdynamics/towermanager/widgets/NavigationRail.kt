package com.fjdynamics.towermanager.widgets

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.fjdynamics.towermanager.ui.theme.ScreenBackgroundColor
import com.fjdynamics.towermanager.ui.theme.TextPrimaryColor
import com.fjdynamics.towermanager.ui.theme.TextSecondaryColor

@Composable
fun NavigationItem(
    @DrawableRes icon: Int,
    title: String,
    onClick: () -> Unit,
    selected: Boolean,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable(enabled = !selected, onClick = onClick)
            .background(if (selected) ScreenBackgroundColor else Color.White)
            .padding(horizontal = 21.dp, vertical = 13.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(10.dp),
    ) {
        Icon(
            painter = painterResource(icon),
            contentDescription = title,
            modifier = Modifier.size(32.dp),
            tint = if (selected) TextPrimaryColor else TextSecondaryColor,
        )
        Text(
            text = title,
            color = if (selected) TextPrimaryColor else TextSecondaryColor,
            fontSize = 21.sp,
        )
    }
}