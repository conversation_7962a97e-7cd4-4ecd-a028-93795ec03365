package com.fjdynamics.towermanager.widgets

import android.widget.Toast
import androidx.compose.runtime.ProvidableCompositionLocal
import androidx.compose.runtime.Stable
import androidx.compose.runtime.staticCompositionLocalOf
import org.jetbrains.annotations.TestOnly

@Stable
val LocalToaster: ProvidableCompositionLocal<Toaster> = staticCompositionLocalOf {
    error("No LocalToaster provided")
}

@Stable
interface Toaster {
    fun toast(msg: String, length: Int = Toast.LENGTH_LONG)
}

@Stable
@TestOnly
object NoOpToaster : Toaster {
    override fun toast(msg: String, length: Int) {
    }
}