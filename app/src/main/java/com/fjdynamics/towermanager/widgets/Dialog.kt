package com.fjdynamics.towermanager.widgets

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.fjdynamics.towermanager.R
import com.fjdynamics.towermanager.ui.theme.AppPrimaryColor
import com.fjdynamics.towermanager.ui.theme.ButtonBackgroundColor
import com.fjdynamics.towermanager.ui.theme.ButtonDisabledBackgroundColor
import com.fjdynamics.towermanager.ui.theme.DividerColor
import com.fjdynamics.towermanager.ui.theme.TextHintColor
import com.fjdynamics.towermanager.ui.theme.TextPrimaryColor

@Composable
fun BaseDialog(
    horizontalPadding: Dp = 160.dp,
    content: @Composable ColumnScope.() -> Unit,
) {
    Dialog(
        onDismissRequest = { },
        properties = DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false,
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = horizontalPadding),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(5.dp),
            content = content
        )
    }
}

@Composable
fun InformDialog(
    title: String = "温馨提示",
    message: String,
    onDismissRequest: () -> Unit,
    onConfirmation: () -> Unit
) {
    BaseDialog {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.size(32.dp))
            Text(
                text = title,
                color = TextPrimaryColor,
                fontSize = 24.sp
            )
            Spacer(modifier = Modifier.size(48.dp))
            Text(
                text = message,
                color = TextPrimaryColor,
                fontSize = 20.sp
            )
            Spacer(modifier = Modifier.size(48.dp))
            DialogBottomButtons(
                onDismissRequest = onDismissRequest,
                onConfirmation = onConfirmation
            )
        }
    }
}

@Composable
fun VerifyPasswordDialog(
    password: String,
    onDismissRequest: () -> Unit,
    onPasswordMatched: () -> Unit,
) {
    BaseDialog {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.size(36.dp))
            Text(
                text = "请输入密码",
                color = TextPrimaryColor,
                fontSize = 24.sp
            )
            Spacer(modifier = Modifier.size(28.dp))

            var input by remember { mutableStateOf("") }
            BasicTextField(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 32.dp)
                    .border(1.dp, DividerColor, RoundedCornerShape(5.dp)),
                value = input,
                onValueChange = { newText ->
                    if (newText.length <= 6 && newText.all { it.isDigit() }) {
                        input = newText
                    }
                },
                singleLine = true,
                visualTransformation = PasswordVisualTransformation(),
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.NumberPassword),
                decorationBox = { innerTextFiled ->
                    Row(
                        modifier = Modifier.padding(horizontal = 21.dp, vertical = 13.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            modifier = Modifier.size(32.dp),
                            painter = painterResource(id = R.drawable.ic_password),
                            contentDescription = null,
                        )
                        Spacer(modifier = Modifier.size(13.dp))
                        Box {
                            if (input.isEmpty()) {
                                Text(
                                    text = "密码",
                                    color = TextHintColor,
                                    fontSize = 21.sp,
                                )
                            }
                            innerTextFiled()
                        }
                    }
                },
                textStyle = TextStyle(color = TextPrimaryColor, fontSize = 21.sp)
            )
            Spacer(modifier = Modifier.size(32.dp))
            val toaster = LocalToaster.current
            DialogBottomButtons(
                onDismissRequest = onDismissRequest,
                onConfirmation = {
                    if (password == input) {
                        onPasswordMatched()
                    } else {
                        toaster.toast("密码错误")
                    }
                },
                confirmEnabled = input.isNotEmpty()
            )
        }
    }
}

@Composable
fun LoadingDialog(msg: String = "") {
    BaseDialog {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 48.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(108.dp),
                color = AppPrimaryColor,
                strokeCap = StrokeCap.Round,
                strokeWidth = 16.dp
            )
            if (msg.isNotEmpty()) {
                Spacer(Modifier.size(32.dp))
                Text(
                    text = msg,
                    color = TextPrimaryColor,
                    fontSize = 21.sp
                )
            }
        }
    }
}

@Composable
fun WarnDialog() {
    Dialog(
        onDismissRequest = {},
        properties = DialogProperties(
            dismissOnBackPress = false,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false,
        )
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 200.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White),
            shape = RoundedCornerShape(5.dp)
        ) {
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                Spacer(modifier = Modifier.size(32.dp))
                Icon(
                    modifier = Modifier.size(64.dp),
                    painter = painterResource(R.drawable.ic_tip_warn),
                    contentDescription = null
                )
            }
        }
    }
}

@Composable
fun DialogBottomButtons(
    onDismissRequest: () -> Unit,
    onConfirmation: () -> Unit,
    confirmEnabled: Boolean = true,
) {
    Row {
        Button(
            modifier = Modifier
                .weight(1f)
                .topBorder(1.dp, DividerColor),
            contentPadding = PaddingValues(vertical = 18.dp),
            shape = RectangleShape,
            colors = ButtonDefaults.buttonColors(containerColor = Color.White),
            onClick = onDismissRequest
        ) {
            Text(
                text = "取消",
                color = TextPrimaryColor,
                fontSize = 21.sp,
                fontWeight = FontWeight.Bold
            )
        }
        Button(
            modifier = Modifier
                .weight(1f),
            contentPadding = PaddingValues(vertical = 18.dp),
            shape = RectangleShape,
            colors = ButtonDefaults.buttonColors(
                containerColor = ButtonBackgroundColor,
                disabledContainerColor = ButtonDisabledBackgroundColor
            ),
            enabled = confirmEnabled,
            onClick = onConfirmation,
        ) {
            Text(
                text = "确定",
                color = Color.White,
                fontSize = 21.sp,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

fun Modifier.topBorder(strokeWidth: Dp, color: Color) = this.then(
    Modifier.drawWithContent {
        drawContent()
        drawLine(
            color = color,
            start = Offset(0f, 0f),
            end = Offset(size.width, 0f),
            strokeWidth = strokeWidth.toPx()
        )
    }
)